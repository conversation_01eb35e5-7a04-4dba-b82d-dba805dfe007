"""ChemSpider API integration service."""

import asyncio
import httpx
import structlog
import time
from typing import List, Optional, Dict, Any

from app.config import settings
from app.models.schemas import Pub<PERSON>hem<PERSON>ompound, MediaImage
from app.utils.query_normalizer import QueryNormalizer
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class ChemSpiderService:
    """Service for interacting with ChemSpider API."""
    
    def __init__(self):
        self.base_url = settings.chemspider_base_url
        self.api_key = settings.chemspider_api_key
        self.timeout = settings.request_timeout_seconds
        self.image_width = settings.chemspider_image_width
        self.image_height = settings.chemspider_image_height
        
    async def _make_request(self, method: str, url: str, headers: Optional[Dict] = None, 
                           json_data: Optional[Dict] = None) -> Optional[Dict[Any, Any]]:
        """Make HTTP request to ChemSpider API."""
        if not self.api_key:
            logger.warning("ChemSpider API key not configured")
            return None
            
        default_headers = {"apikey": self.api_key}
        if headers:
            default_headers.update(headers)
            
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "POST":
                    default_headers["Content-Type"] = "application/json"
                    response = await client.post(url, headers=default_headers, json=json_data)
                else:
                    response = await client.get(url, headers=default_headers)
                    
                response.raise_for_status()
                return response.json()
        except httpx.HTTPError as e:
            logger.warning("ChemSpider API request failed", url=url, error=str(e))
            return None
        except Exception as e:
            logger.error("Unexpected error in ChemSpider request", url=url, error=str(e))
            return None
    
    async def search_compound_by_name(self, name: str) -> Optional[int]:
        """
        Search for compound CSID by name using ChemSpider 2-step process.
        
        Args:
            name: Chemical name or formula
            
        Returns:
            CSID if found, None otherwise
        """
        # Step 1: Submit name filter query
        url = f"{self.base_url}/filter/name"
        query_data = {
            "name": name,
            "orderBy": "recordId",
            "orderDirection": "ascending"
        }
        
        logger.info("Searching ChemSpider by name", name=name)
        
        data = await self._make_request("POST", url, json_data=query_data)
        if not data or "queryId" not in data:
            logger.info("No ChemSpider queryId received", name=name)
            return None
            
        query_id = data["queryId"]
        
        # Step 2: Get results (with retry for async processing)
        results_url = f"{self.base_url}/filter/{query_id}/results"
        
        # Wait a bit for processing
        await asyncio.sleep(1)
        
        for attempt in range(3):  # Retry up to 3 times
            results = await self._make_request("GET", results_url)
            if results:
                # ChemSpider returns {"results": [csid1, csid2, ...]}
                if isinstance(results, dict) and "results" in results:
                    result_list = results["results"]
                    if result_list and len(result_list) > 0:
                        csid = result_list[0]  # Take first CSID
                        logger.info("Found ChemSpider CSID", name=name, csid=csid)
                        return csid
                # Fallback for direct list format
                elif isinstance(results, list) and len(results) > 0:
                    csid = results[0]
                    logger.info("Found ChemSpider CSID", name=name, csid=csid)
                    return csid
            
            if attempt < 2:  # Don't sleep on last attempt
                await asyncio.sleep(2)  # Wait longer between retries
        
        logger.info("No ChemSpider CSID found", name=name)
        return None
    
    async def search_compound_by_formula(self, formula: str) -> Optional[int]:
        """
        Search for compound CSID by molecular formula using ChemSpider.
        
        Args:
            formula: Molecular formula (e.g., H2SO4)
            
        Returns:
            CSID if found, None otherwise
        """
        # Step 1: Submit formula filter query
        url = f"{self.base_url}/filter/formula"
        query_data = {
            "formula": formula,
            "orderBy": "recordId", 
            "orderDirection": "ascending"
        }
        
        logger.info("Searching ChemSpider by formula", formula=formula)
        
        data = await self._make_request("POST", url, json_data=query_data)
        if not data or "queryId" not in data:
            logger.info("No ChemSpider queryId received for formula", formula=formula)
            return None
            
        query_id = data["queryId"]
        
        # Step 2: Get results
        results_url = f"{self.base_url}/filter/{query_id}/results"
        
        # Wait for processing
        await asyncio.sleep(1)
        
        for attempt in range(3):
            results = await self._make_request("GET", results_url)
            if results:
                # ChemSpider returns {"results": [csid1, csid2, ...]}
                if isinstance(results, dict) and "results" in results:
                    result_list = results["results"]
                    if result_list and len(result_list) > 0:
                        csid = result_list[0]  # Take first CSID
                        logger.info("Found ChemSpider CSID by formula", formula=formula, csid=csid)
                        return csid
                # Fallback for direct list format
                elif isinstance(results, list) and len(results) > 0:
                    csid = results[0]
                    logger.info("Found ChemSpider CSID by formula", formula=formula, csid=csid)
                    return csid
                
            if attempt < 2:
                await asyncio.sleep(2)
        
        logger.info("No ChemSpider CSID found by formula", formula=formula)
        return None
    
    async def get_compound_properties(self, csid: int) -> Optional[PubChemCompound]:
        """
        Get compound properties by CSID.
        
        Args:
            csid: ChemSpider Record ID
            
        Returns:
            PubChemCompound object if successful, None otherwise
        """
        fields = [
            "SMILES",
            "Formula", 
            "CommonName",
            "InChI",
            "InChIKey",
            "MolecularWeight"
        ]
        
        field_list = ",".join(fields)
        url = f"{self.base_url}/records/{csid}/details?fields={field_list}"
        
        logger.info("Getting ChemSpider compound properties", csid=csid)
        
        data = await self._make_request("GET", url)
        if data:
            # ChemSpider returns data with lowercase field names
            return PubChemCompound(
                cid=csid,  # Using CSID as cid for compatibility
                title=data.get("commonName") or f"Compound {csid}",
                iupac_name=data.get("commonName"),
                smiles=data.get("smiles"),
                inchi_key=data.get("inchiKey"),
                molecular_formula=data.get("formula"),
                molecular_weight=data.get("molecularWeight")
            )
        
        logger.warning("Failed to get ChemSpider compound properties", csid=csid)
        return None
    
    async def get_compound_images(self, csid: int) -> List[MediaImage]:
        """
        Get compound structure images from ChemSpider.

        Args:
            csid: ChemSpider Record ID

        Returns:
            List of MediaImage objects
        """
        images = []

        # Get image data from ChemSpider
        url = f"{self.base_url}/records/{csid}/image"

        logger.info("Getting ChemSpider image", csid=csid)

        data = await self._make_request("GET", url)
        if data and "image" in data:
            # ChemSpider returns base64 encoded image
            base64_image = data["image"]

            # Create data URL for direct embedding
            data_url = f"data:image/png;base64,{base64_image}"

            images.append(MediaImage(
                url=data_url,
                thumbnail=data_url,  # Same as main image
                format="png",
                width=self.image_width,
                height=self.image_height,
                source="chemspider",
                license="Royal Society of Chemistry",
                attribution="ChemSpider, Royal Society of Chemistry",
                caption="Cấu trúc 2D"
            ))

            logger.info("Generated ChemSpider image", csid=csid, size=len(base64_image))
        else:
            logger.warning("No image data from ChemSpider", csid=csid)

        return images
    
    async def search_compound(self, query: str) -> Optional[PubChemCompound]:
        """
        Search for compound using multiple strategies with caching.
        
        Args:
            query: Chemical name or formula
            
        Returns:
            PubChemCompound if found, None otherwise
        """
        # Check cache first
        cached_data = await cache_manager.get_compound_cache(query)
        if cached_data:
            logger.info("Found compound in cache", query=query)
            return PubChemCompound(**cached_data)
        
        # Use basic normalization first
        primary_query, alternatives = QueryNormalizer.normalize_query(query)
        
        # Try all query variations
        all_queries = [primary_query] + alternatives

        for q in all_queries:
            try:
                csid = None

                # If it looks like a chemical formula, try formula search first
                if QueryNormalizer.is_chemical_formula(q):
                    # Try both original case and uppercase (ChemSpider prefers uppercase formulas)
                    for formula_variant in [q.upper(), q]:
                        csid = await self.search_compound_by_formula(formula_variant)
                        if csid:
                            logger.info("Found compound via formula search", query=q, formula=formula_variant, csid=csid)
                            break

                # If not found via formula or not a formula, try name search
                if not csid:
                    csid = await self.search_compound_by_name(q)
                    if csid:
                        logger.info("Found compound via name search", query=q, csid=csid)
                
                if csid:
                    # Get compound properties
                    compound = await self.get_compound_properties(csid)
                    if compound:
                        # Cache the result
                        await cache_manager.set_compound_cache(query, compound.model_dump())
                        logger.info("Successfully found compound", query=query, csid=csid)
                        return compound
            except Exception as e:
                logger.warning("Error searching compound variation", query=q, error=str(e))
                continue
        
        logger.info("No compound found in ChemSpider", query=query)
        return None
