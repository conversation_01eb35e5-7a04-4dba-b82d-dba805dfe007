"""
Test protected endpoint
"""

import asyncio
import httpx


async def test_protected_endpoint():
    """Test protected media search endpoint"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        print("=== Testing Protected Endpoint ===\n")
        
        # 1. Test without token (should fail)
        print("1. Testing media search without token...")
        search_data = {
            "query": "water",
            "types": ["image"],
            "limit": 1
        }
        
        response = await client.post(
            f"{base_url}/chem/media/search",
            json=search_data
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print("✓ Correctly rejected request without token\n")
        else:
            print(f"✗ Unexpected response: {response.text}\n")
        
        # 2. Get valid token
        print("2. Getting valid token...")
        register_data = {
            "client_name": "Protected Test Client",
            "description": "Test protected endpoints"
        }
        
        response = await client.post(
            f"{base_url}/chem/auth/register-client",
            json=register_data
        )
        
        if response.status_code == 200:
            registration = response.json()
            client_id = registration['client_id']
            client_secret = registration['client_secret']
            
            # Generate token
            token_data = {
                "client_id": client_id,
                "client_secret": client_secret
            }
            
            response = await client.post(
                f"{base_url}/chem/auth/token",
                json=token_data
            )
            
            if response.status_code == 200:
                token_response = response.json()
                access_token = token_response['access_token']
                print(f"✓ Token obtained: {access_token[:20]}...\n")
                
                # 3. Test with valid token
                print("3. Testing media search with valid token...")
                headers = {"Authorization": f"Bearer {access_token}"}
                
                try:
                    response = await client.get(
                        f"{base_url}/chemistry/media/search",
                        params=search_data,
                        headers=headers
                    )
                    print(f"Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✓ Media search successful!")
                        print(f"Query: {result.get('query')}")
                        print(f"Images found: {len(result.get('images', []))}")
                        print(f"Sources: {result.get('meta', {}).get('sources', [])}")
                        return True
                    else:
                        print(f"✗ Media search failed: {response.text}")
                        
                except Exception as e:
                    print(f"✗ Request failed: {e}")
            else:
                print(f"✗ Token generation failed: {response.text}")
        else:
            print(f"✗ Client registration failed: {response.text}")
    
    return False


if __name__ == "__main__":
    success = asyncio.run(test_protected_endpoint())
    if success:
        print("\n✅ Protected endpoint test passed!")
    else:
        print("\n❌ Protected endpoint test failed!")
