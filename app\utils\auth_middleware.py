"""
Middleware xác thực cho API endpoints
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import logging

from app.services.auth_service import auth_service

logger = logging.getLogger(__name__)
security = HTTPBearer()


async def verify_api_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    Dependency để xác thực API token
    
    Returns:
        client_id: ID của client nếu token hợp lệ
        
    Raises:
        HTTPException: Nếu token không hợp lệ
    """
    try:
        verification = await auth_service.verify_token(credentials.credentials)
        
        if not verification.valid:
            logger.warning(f"Invalid token used: {verification.message}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": "INVALID_TOKEN",
                    "message": verification.message or "Token không hợp lệ"
                },
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.info(f"Token verified successfully for client: {verification.client_id}")
        return verification.client_id
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "VERIFICATION_ERROR",
                "message": "Không thể xác thực token"
            }
        )


async def optional_auth(
    authorization: Optional[str] = Depends(lambda: None)
) -> Optional[str]:
    """
    Optional authentication dependency
    
    Returns:
        client_id nếu có token hợp lệ, None nếu không có token
    """
    if not authorization:
        return None
        
    try:
        # Parse Bearer token
        if not authorization.startswith("Bearer "):
            return None
            
        token = authorization[7:]  # Remove "Bearer " prefix
        verification = await auth_service.verify_token(token)
        
        if verification.valid:
            return verification.client_id
        else:
            return None
            
    except Exception as e:
        logger.warning(f"Optional auth failed: {e}")
        return None
