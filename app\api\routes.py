"""API routes for Chemistry Media API."""

import asyncio
import time
from typing import List, Optional
import structlog
from fastapi import APIRouter, HTTPException, status, Depends, Query

from app.models.schemas import (
    MediaSearchRequest,
    MediaSearchResponse,
    ErrorResponse,
    ChemicalCompound,
    SearchMetadata,
    MediaImage,
    create_success_response,
    create_error_response
)
from app.services.chemspider_service import ChemSpiderService
from app.services.pubchem_service import PubChemService
from app.services.wikipedia_service import WikipediaService
from app.services.youtube_service import YouTubeService
from app.api.auth_endpoints import router as auth_router
from app.utils.auth_middleware import verify_api_token
from app.utils.cache import cache_manager
from app.utils.query_normalizer import QueryNormalizer

logger = structlog.get_logger()

router = APIRouter()

# Initialize services
chemspider_service = ChemSpiderService()
pubchem_service = PubChemService()
wikipedia_service = WikipediaService()
youtube_service = YouTubeService()

# Include auth routes
router.include_router(auth_router, prefix="/auth", tags=["Authentication"])





@router.post(
    "/media/search",
    responses={
        400: {"model": ErrorResponse},
        502: {"model": ErrorResponse}
    },
    summary="Tìm kiếm hình ảnh và video theo tên hoặc công thức hóa học",
    description="""
    Tìm kiếm hình ảnh và video liên quan đến chất hóa học.

    **Input**: Tên chất (tiếng Việt/Anh) hoặc công thức hóa học

    **Output**: Danh sách hình ảnh và video kèm metadata, nguồn, bản quyền

    **Nguồn dữ liệu**:
    - PubChem: Cấu trúc 2D, metadata hóa học
    - Wikipedia/Wikimedia Commons: Hình ảnh minh họa
    - YouTube: Video giáo dục và thí nghiệm
    """
)
async def search_media(
    request: MediaSearchRequest,
    client_id: str = Depends(verify_api_token)
):
    """Search for chemistry-related media."""

    start_time = time.time()
    sources_used = []

    # Check cache first
    cached_result = await cache_manager.get_search_cache(
        request.query, request.types, request.limit, request.lang
    )
    if cached_result:
        logger.info("Found search result in cache", query=request.query)
        # Update response time for cached result
        cached_result["meta"]["took_ms"] = int((time.time() - start_time) * 1000)
        return create_success_response(
            data=cached_result,
            message="Kết quả từ cache"
        )

    try:
        logger.info("Starting media search", query=request.query, types=request.types)

        # Step 1: Resolve query (formula resolution + translation)
        resolved_query = request.query

        # Try comprehensive query normalization
        try:
            normalized_primary, normalized_alternatives = await QueryNormalizer.normalize_query_with_apis(
                request.query,
                chemspider_service=chemspider_service,
                pubchem_service=pubchem_service
            )

            if normalized_primary != request.query:
                resolved_query = normalized_primary
                logger.info("Resolved query via normalization",
                          original=request.query, resolved=resolved_query,
                          alternatives=normalized_alternatives[:3])
        except Exception as e:
            logger.warning("Query normalization failed", query=request.query, error=str(e))

        # Step 2: Search for chemical compound data
        resolved_compound = None
        chemspider_images = []

        if "image" in request.types:
            try:
                # Use resolved query for better results
                compound = await chemspider_service.search_compound(resolved_query)
                if compound:
                    resolved_compound = ChemicalCompound(
                        cid=compound.cid,
                        title=compound.title,
                        iupac_name=compound.iupac_name,
                        smiles=compound.smiles,
                        inchi_key=compound.inchi_key
                    )

                    # Get ChemSpider structure images
                    chemspider_images = await chemspider_service.get_compound_images(compound.cid)
                    sources_used.append("chemspider")

                    logger.info("Resolved compound from ChemSpider",
                              csid=compound.cid, title=compound.title)
            except Exception as e:
                logger.warning("ChemSpider search failed", error=str(e))
        
        # Step 2: Search for images and videos in parallel
        tasks = []
        
        # Wikipedia/Commons images
        if "image" in request.types:
            tasks.append(("wikipedia", wikipedia_service.search_images(
                resolved_query,
                limit=request.limit // len([t for t in request.types if t != "gif"]) if len(request.types) > 1 else request.limit
            )))

        # Wikipedia/Commons GIFs
        if "gif" in request.types:
            tasks.append(("wikipedia_gif", wikipedia_service.search_commons_gifs(
                resolved_query,
                limit=request.limit // len(request.types) if len(request.types) > 1 else request.limit
            )))

        # YouTube videos
        if "video" in request.types:
            tasks.append(("youtube", youtube_service.search_educational_videos(
                resolved_query,
                limit=request.limit // len([t for t in request.types if t != "gif"]) if len(request.types) > 1 else request.limit
            )))
        
        # Execute searches in parallel
        results = {}
        if tasks:
            task_results = await asyncio.gather(
                *[task[1] for task in tasks],
                return_exceptions=True
            )
            
            for (source, result) in zip([task[0] for task in tasks], task_results):
                if isinstance(result, Exception):
                    logger.warning(f"{source} search failed", error=str(result))
                    results[source] = []
                else:
                    results[source] = result
                    if result:  # Only add to sources if we got results
                        sources_used.append(source)
        
        # Step 3: Combine results
        all_images = chemspider_images + results.get("wikipedia", [])
        all_gifs = results.get("wikipedia_gif", [])
        all_videos = results.get("youtube", [])

        # Step 4: Apply limits and ranking
        # For images: prioritize structure images from ChemSpider
        final_images = []
        if "image" in request.types:
            # Add ChemSpider images first (structure priority)
            structure_images = [img for img in all_images if img.source == "chemspider"]
            illustration_images = [img for img in all_images if img.source != "chemspider"]

            final_images = structure_images + illustration_images
            final_images = final_images[:request.limit]

        # For GIFs: convert WikipediaImage to MediaImage format
        final_gifs = []
        if "gif" in request.types:
            for gif in all_gifs:
                final_gifs.append(MediaImage(
                    url=gif.url,
                    thumbnail=gif.url,
                    format="gif",
                    width=gif.width,
                    height=gif.height,
                    source="wikipedia",
                    license=gif.license,
                    attribution=gif.attribution,
                    caption=gif.description or f"GIF animation về {request.query}"
                ))
            final_gifs = final_gifs[:request.limit]

        # For videos: already sorted by relevance in YouTube service
        final_videos = []
        if "video" in request.types:
            final_videos = all_videos[:request.limit]
        
        # Step 5: Calculate response time
        end_time = time.time()
        took_ms = int((end_time - start_time) * 1000)
        
        response = MediaSearchResponse(
            query=request.query,
            resolved=resolved_compound,
            images=final_images,
            gifs=final_gifs,
            videos=final_videos,
            meta=SearchMetadata(
                took_ms=took_ms,
                sources=sources_used
            )
        )

        # Cache the search result
        await cache_manager.set_search_cache(
            request.query, request.types, request.limit, request.lang,
            response.model_dump()
        )

        logger.info("Media search completed",
                   query=request.query,
                   images_count=len(final_images),
                   gifs_count=len(final_gifs),
                   videos_count=len(final_videos),
                   took_ms=took_ms,
                   sources=sources_used)

        return create_success_response(
            data=response.model_dump(),
            message="Tìm kiếm thành công"
        )

    except Exception as e:
        logger.error("Media search failed", query=request.query, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=create_error_response(
                message=f"Không thể thực hiện tìm kiếm: {str(e)}"
            )
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for the chemistry media service."""
    result = create_success_response(
        data={
            "status": "healthy",
            "service": "chemistry-media-api",
            "timestamp": time.time()
        },
        message="Service is healthy"
    )
    logger.info("Health check response", response=result)
    return result
