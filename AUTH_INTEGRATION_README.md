# Auth System Integration

Hệ thống xác thực API đã được tích hợp thành công vào Chemistry Media API.

## Tổng quan

Auth system sử dụng mô hình Client Credentials với JWT tokens để bảo vệ API endpoints:

1. **Client Registration**: Đăng ký để nhận ClientID/ClientSecret
2. **Token Generation**: Sử dụng credentials để tạo access token
3. **API Access**: Sử dụng token để truy cập protected endpoints

## Endpoints Auth

### 1. Đăng ký Client
```
POST /chem/auth/register-client
```

**Request:**
```json
{
  "client_name": "My App",
  "description": "Ứng dụng hóa học của tôi",
  "contact_email": "<EMAIL>"
}
```

**Response:**
```json
{
  "client_id": "client_abc123...",
  "client_secret": "secret_xyz789...",
  "client_name": "My App",
  "created_at": "2025-08-12T09:28:40.165000",
  "message": "Client đã được đăng ký thành công..."
}
```

⚠️ **Quan trọng**: Client Secret chỉ hiển thị 1 lần duy nhất!

### 2. Tạo Access Token
```
POST /chem/auth/token
```

**Request:**
```json
{
  "client_id": "client_abc123...",
  "client_secret": "secret_xyz789..."
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "created_at": "2025-08-12T09:28:40.172000"
}
```

### 3. Sử dụng Protected Endpoints

Tất cả API endpoints hiện tại đều yêu cầu authentication:

```
POST /chem/media/search
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Request:**
```json
{
  "query": "water",
  "types": ["image", "video"],
  "limit": 5
}
```

## Cấu hình

Các cấu hình auth trong `app/config.py`:

```python
# Authentication Configuration
secret_key: str = "your-secret-key-change-this-in-production"
algorithm: str = "HS256"
access_token_expire_minutes: int = 60  # 1 hour

# MongoDB Configuration
mongodb_url: str = "mongodb://localhost:27017"
mongodb_database: str = "chemistry_api"
```

## Dependencies Mới

Đã thêm vào `requirements.txt`:
- `PyJWT==2.8.0` - JWT token handling
- `motor==3.7.1` - MongoDB async driver

## Cấu trúc Files

```
app/
├── services/
│   └── auth_service.py          # Core auth logic
├── models/
│   └── auth_models.py           # Pydantic models
├── api/
│   └── auth_endpoints.py        # Auth API endpoints
└── utils/
    └── auth_middleware.py       # Authentication middleware
```

## Testing

Chạy test để kiểm tra tích hợp:

```bash
# Test auth endpoints
python test_auth_simple.py

# Test protected endpoints
python test_protected_endpoint.py
```

## Bảo mật

1. **Client Secret**: Được hash trước khi lưu vào database
2. **Tokens**: Được hash và lưu trữ để có thể revoke
3. **JWT**: Signed với secret key, có thời gian hết hạn
4. **Database**: Sử dụng indexes để tối ưu performance

## Sử dụng

1. Đăng ký client để nhận credentials
2. Sử dụng credentials để tạo access token
3. Gửi token trong header `Authorization: Bearer <token>`
4. Token có thời hạn 1 giờ, cần tạo mới khi hết hạn

## Monitoring

- Logs được ghi chi tiết cho tất cả auth operations
- Health check endpoint: `/chem/auth/health`
- Tự động cleanup expired tokens
