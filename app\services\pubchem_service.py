"""PubChem API integration service."""

import asyncio
import httpx
import structlog
from typing import List, Optional, Dict, Any

from app.config import settings
from app.models.schemas import Pub<PERSON>hemCompound
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class PubChemService:
    """Service for interacting with PubChem API."""
    
    def __init__(self):
        self.base_url = "https://pubchem.ncbi.nlm.nih.gov/rest/pug"
        self.timeout = settings.request_timeout_seconds
    
    async def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make HTTP request to PubChem API."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.warning("PubChem API request failed", url=url, error=str(e))
            return None
    
    async def search_by_formula(self, formula: str) -> Optional[PubChemCompound]:
        """
        Search compound by molecular formula.
        
        Args:
            formula: Molecular formula (e.g., H2SO4)
            
        Returns:
            PubChemCompound if found, None otherwise
        """
        url = f"{self.base_url}/compound/formula/{formula}/JSON"
        
        logger.info("Searching PubChem by formula", formula=formula)
        
        data = await self._make_request(url)
        if not data or "PC_Compounds" not in data:
            return None
        
        # Get first compound
        compound_data = data["PC_Compounds"][0]
        cid = compound_data["id"]["id"]["cid"]
        
        # Get detailed properties
        return await self.get_compound_properties(cid)
    
    async def search_by_name(self, name: str) -> Optional[PubChemCompound]:
        """
        Search compound by name.
        
        Args:
            name: Chemical name
            
        Returns:
            PubChemCompound if found, None otherwise
        """
        url = f"{self.base_url}/compound/name/{name}/JSON"
        
        logger.info("Searching PubChem by name", name=name)
        
        data = await self._make_request(url)
        if not data or "PC_Compounds" not in data:
            return None
        
        # Get first compound
        compound_data = data["PC_Compounds"][0]
        cid = compound_data["id"]["id"]["cid"]
        
        return await self.get_compound_properties(cid)
    
    async def get_compound_properties(self, cid: int) -> Optional[PubChemCompound]:
        """
        Get compound properties by CID.
        
        Args:
            cid: PubChem Compound ID
            
        Returns:
            PubChemCompound if successful, None otherwise
        """
        # Get synonyms (names)
        synonyms_url = f"{self.base_url}/compound/cid/{cid}/synonyms/JSON"
        synonyms_data = await self._make_request(synonyms_url)
        
        # Get properties
        props_url = f"{self.base_url}/compound/cid/{cid}/property/MolecularFormula,MolecularWeight,IUPACName,InChIKey,CanonicalSMILES/JSON"
        props_data = await self._make_request(props_url)
        
        if not props_data or "PropertyTable" not in props_data:
            return None
        
        properties = props_data["PropertyTable"]["Properties"][0]
        
        # Extract title from synonyms
        title = None
        if synonyms_data and "InformationList" in synonyms_data:
            synonyms = synonyms_data["InformationList"]["Information"][0]["Synonym"]
            # Use first synonym as title
            title = synonyms[0] if synonyms else f"Compound {cid}"
        
        return PubChemCompound(
            cid=cid,
            title=title,
            iupac_name=properties.get("IUPACName"),
            smiles=properties.get("CanonicalSMILES"),
            inchi_key=properties.get("InChIKey"),
            molecular_formula=properties.get("MolecularFormula"),
            molecular_weight=properties.get("MolecularWeight")
        )
    
    async def search_compound(self, query: str) -> Optional[PubChemCompound]:
        """
        Search for compound using multiple strategies with caching.
        
        Args:
            query: Chemical name or formula
            
        Returns:
            PubChemCompound if found, None otherwise
        """
        # Check cache first
        cache_key = f"pubchem_{query.lower()}"
        cached_data = await cache_manager.get(cache_key)
        if cached_data:
            logger.info("Found PubChem compound in cache", query=query)
            return PubChemCompound(**cached_data)
        
        compound = None

        # Check if query looks like a chemical formula
        from app.utils.query_normalizer import QueryNormalizer
        is_formula = QueryNormalizer.is_chemical_formula(query)

        # If it's a formula, try formula search first
        if is_formula:
            try:
                compound = await self.search_by_formula(query)
                if compound:
                    logger.info("Found compound via formula search", query=query, cid=compound.cid)
            except Exception as e:
                logger.warning("PubChem formula search failed", query=query, error=str(e))

        # If not found via formula or not a formula, try name search
        if not compound:
            for name_variant in [query, query.upper()]:
                try:
                    compound = await self.search_by_name(name_variant)
                    if compound:
                        logger.info("Found compound via name search", query=name_variant, cid=compound.cid)
                        break
                except Exception as e:
                    logger.warning("PubChem name search failed", query=name_variant, error=str(e))
        
        # Cache the result
        if compound:
            await cache_manager.set(cache_key, compound.model_dump())
            logger.info("Successfully found PubChem compound", query=query, cid=compound.cid)
        
        return compound
