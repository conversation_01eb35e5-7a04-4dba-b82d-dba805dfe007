"""Wikipedia and Wikimedia Commons integration service."""

import asyncio
import httpx
import structlog
from typing import List, Optional, Dict, Any
from urllib.parse import quote

from app.config import settings
from app.models.schemas import WikipediaImage, MediaImage
from app.utils.query_normalizer import QueryNormalizer
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class WikipediaService:
    """Service for searching images from Wikipedia and Wikimedia Commons."""
    
    def __init__(self):
        self.wikipedia_api = settings.wikipedia_api_url
        self.commons_api = settings.commons_api_url
        self.timeout = settings.request_timeout_seconds
        
    async def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict[Any, Any]]:
        """Make HTTP request to Wikipedia/Commons API."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPError as e:
            logger.warning("Wikipedia API request failed", url=url, error=str(e))
            return None
        except Exception as e:
            logger.error("Unexpected error in Wikipedia request", url=url, error=str(e))
            return None
    
    async def search_wikipedia_page(self, query: str) -> Optional[str]:
        """
        Search for Wikipedia page title.
        
        Args:
            query: Search term
            
        Returns:
            Page title if found, None otherwise
        """
        params = {
            "action": "query",
            "format": "json",
            "list": "search",
            "srsearch": query,
            "srlimit": 1
        }
        
        url = "https://en.wikipedia.org/w/api.php"
        
        logger.info("Searching Wikipedia page", query=query)
        
        data = await self._make_request(url, params)
        if data and "query" in data and "search" in data["query"]:
            search_results = data["query"]["search"]
            if search_results:
                title = search_results[0]["title"]
                logger.info("Found Wikipedia page", query=query, title=title)
                return title
        
        logger.info("No Wikipedia page found", query=query)
        return None
    
    async def get_page_images(self, page_title: str) -> List[WikipediaImage]:
        """
        Get images from a Wikipedia page.
        
        Args:
            page_title: Wikipedia page title
            
        Returns:
            List of WikipediaImage objects
        """
        params = {
            "action": "query",
            "format": "json",
            "titles": page_title,
            "prop": "images",
            "imlimit": 10
        }
        
        url = "https://en.wikipedia.org/w/api.php"
        
        logger.info("Getting Wikipedia page images", page_title=page_title)
        
        data = await self._make_request(url, params)
        if not data or "query" not in data or "pages" not in data["query"]:
            return []
        
        pages = data["query"]["pages"]
        page_id = list(pages.keys())[0]
        
        if page_id == "-1" or "images" not in pages[page_id]:
            return []
        
        images = []
        image_titles = [img["title"] for img in pages[page_id]["images"]]
        
        # Get image info for each image
        for img_title in image_titles[:5]:  # Limit to 5 images
            img_info = await self.get_image_info(img_title)
            if img_info:
                images.append(img_info)
        
        logger.info("Found Wikipedia page images", page_title=page_title, count=len(images))
        return images
    
    async def get_image_info(self, image_title: str) -> Optional[WikipediaImage]:
        """
        Get detailed information about an image.
        
        Args:
            image_title: Image title (e.g., "File:Example.jpg")
            
        Returns:
            WikipediaImage object if successful, None otherwise
        """
        params = {
            "action": "query",
            "format": "json",
            "titles": image_title,
            "prop": "imageinfo",
            "iiprop": "url|size|extmetadata",
            "iiurlwidth": 300
        }
        
        url = "https://commons.wikimedia.org/w/api.php"
        
        data = await self._make_request(url, params)
        if not data or "query" not in data or "pages" not in data["query"]:
            return None
        
        pages = data["query"]["pages"]
        page_id = list(pages.keys())[0]
        
        if page_id == "-1" or "imageinfo" not in pages[page_id]:
            return None
        
        img_info = pages[page_id]["imageinfo"][0]
        
        # Extract license and attribution from metadata
        license_info = self._extract_license_info(img_info.get("extmetadata", {}))
        
        # Skip if license is not suitable for educational use
        if not self._is_suitable_license(license_info["license"]):
            return None
        
        return WikipediaImage(
            url=img_info["url"],
            title=image_title,
            description=img_info.get("extmetadata", {}).get("ImageDescription", {}).get("value", ""),
            license=license_info["license"],
            attribution=license_info["attribution"],
            width=img_info.get("width"),
            height=img_info.get("height")
        )
    
    def _extract_license_info(self, metadata: Dict) -> Dict[str, str]:
        """Extract license and attribution from image metadata."""
        license_name = "Unknown"
        attribution = "Wikimedia Commons"
        
        if "LicenseShortName" in metadata:
            license_name = metadata["LicenseShortName"]["value"]
        elif "License" in metadata:
            license_name = metadata["License"]["value"]
        
        if "Artist" in metadata:
            attribution = f"Wikimedia Commons - {metadata['Artist']['value']}"
        elif "Attribution" in metadata:
            attribution = metadata["Attribution"]["value"]
        
        return {
            "license": license_name,
            "attribution": attribution
        }
    
    def _is_suitable_license(self, license_name: str) -> bool:
        """Check if license is suitable for educational use."""
        suitable_licenses = [
            "CC0",
            "CC BY",
            "CC BY-SA",
            "CC BY 2.0",
            "CC BY 3.0", 
            "CC BY 4.0",
            "CC BY-SA 2.0",
            "CC BY-SA 3.0",
            "CC BY-SA 4.0",
            "Public domain",
            "PD"
        ]
        
        license_lower = license_name.lower()
        return any(suitable.lower() in license_lower for suitable in suitable_licenses)
    
    async def search_commons_images(self, query: str, limit: int = 5) -> List[WikipediaImage]:
        """
        Search for images directly on Wikimedia Commons.
        
        Args:
            query: Search term
            limit: Maximum number of images to return
            
        Returns:
            List of WikipediaImage objects
        """
        params = {
            "action": "query",
            "format": "json",
            "list": "search",
            "srnamespace": 6,  # File namespace
            "srsearch": f"filetype:bitmap|drawing|multimedia {query}",
            "srlimit": limit * 2  # Get more to filter out unsuitable ones
        }
        
        url = self.commons_api
        
        logger.info("Searching Wikimedia Commons", query=query)
        
        data = await self._make_request(url, params)
        if not data or "query" not in data or "search" not in data["query"]:
            return []
        
        search_results = data["query"]["search"]
        images = []
        
        for result in search_results:
            if len(images) >= limit:
                break
                
            img_info = await self.get_image_info(result["title"])
            if img_info:
                images.append(img_info)
        
        logger.info("Found Commons images", query=query, count=len(images))
        return images

    async def search_commons_gifs(self, query: str, limit: int = 3) -> List[WikipediaImage]:
        """
        Search for GIF animations on Wikimedia Commons.

        Args:
            query: Search term
            limit: Maximum number of GIFs to return

        Returns:
            List of WikipediaImage objects
        """
        # Search specifically for GIF files
        params = {
            "action": "query",
            "format": "json",
            "list": "search",
            "srnamespace": 6,  # File namespace
            "srsearch": f"{query} .gif",
            "srlimit": limit * 3  # Get more to filter for actual GIFs
        }

        url = self.commons_api

        logger.info("Searching Wikimedia Commons for GIFs", query=query)

        data = await self._make_request(url, params)
        if not data or "query" not in data or "search" not in data["query"]:
            return []

        search_results = data["query"]["search"]
        gif_images = []

        for result in search_results:
            if len(gif_images) >= limit:
                break

            # Process all results and check if they're actually GIFs
            img_info = await self.get_image_info(result["title"])
            if img_info and img_info.url.lower().endswith('.gif'):
                gif_images.append(img_info)

        logger.info("Found Commons GIFs", query=query, count=len(gif_images))
        return gif_images
    
    async def search_images(self, query: str, limit: int = 5) -> List[MediaImage]:
        """
        Search for images using both Wikipedia pages and Commons search.

        Args:
            query: Chemical name or formula
            limit: Maximum number of images to return

        Returns:
            List of MediaImage objects
        """
        # Check cache first
        cached_images = await cache_manager.get_images_cache(query, "wikipedia")
        if cached_images:
            logger.info("Found Wikipedia images in cache", query=query, count=len(cached_images))
            return [MediaImage(**img) for img in cached_images[:limit]]

        # Get query variations
        primary_query, alternatives = QueryNormalizer.normalize_query(query)
        all_queries = [primary_query] + alternatives

        all_images = []
        
        # Search Wikipedia pages first
        for q in all_queries[:3]:  # Limit to avoid too many requests
            page_title = await self.search_wikipedia_page(q)
            if page_title:
                page_images = await self.get_page_images(page_title)
                all_images.extend(page_images)
                break  # Found a page, no need to try other queries
        
        # Search Commons directly for static images
        commons_images = await self.search_commons_images(primary_query, limit // 2)
        all_images.extend(commons_images)

        # Search Commons for GIF animations
        try:
            print(f"DEBUG: Searching for GIFs with query: {primary_query}")
            gif_images = await self.search_commons_gifs(primary_query, limit // 2)
            print(f"DEBUG: Found {len(gif_images)} GIFs")
            all_images.extend(gif_images)
            logger.info("Added GIF images", count=len(gif_images))
        except Exception as e:
            print(f"DEBUG: GIF search failed: {e}")
            logger.error("Failed to search Commons GIFs", error=str(e))
        
        # Convert to MediaImage format and remove duplicates
        media_images = []
        seen_urls = set()

        print(f"DEBUG: Converting {len(all_images)} images to MediaImage format")
        for i, img in enumerate(all_images):
            print(f"DEBUG: Image {i+1}: {img.url} (format: {self._get_image_format(img.url)})")
            if img.url not in seen_urls and len(media_images) < limit:
                seen_urls.add(img.url)
                
                media_images.append(MediaImage(
                    url=img.url,
                    thumbnail=img.url,  # Use same URL as thumbnail
                    format=self._get_image_format(img.url),
                    width=img.width,
                    height=img.height,
                    source="wikipedia",
                    license=img.license,
                    attribution=img.attribution,
                    caption=img.description or f"Hình ảnh minh họa {query}"
                ))
        
        # Cache the results
        if media_images:
            await cache_manager.set_images_cache(
                query, "wikipedia", [img.model_dump() for img in media_images]
            )

        logger.info("Total Wikipedia/Commons images found", query=query, count=len(media_images))
        return media_images
    
    def _get_image_format(self, url: str) -> str:
        """Extract image format from URL."""
        if url.lower().endswith('.png'):
            return "png"
        elif url.lower().endswith('.jpg') or url.lower().endswith('.jpeg'):
            return "jpg"
        elif url.lower().endswith('.svg'):
            return "svg"
        elif url.lower().endswith('.gif'):
            return "gif"
        else:
            return "unknown"
