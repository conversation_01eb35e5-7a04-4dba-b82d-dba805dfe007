"""YouTube Data API integration service."""

import asyncio
import httpx
import structlog
from typing import List, Optional, Dict, Any
from urllib.parse import quote

from app.config import settings
from app.models.schemas import YouTubeVideo, MediaVideo
from app.utils.query_normalizer import QueryNormalizer
from app.utils.cache import cache_manager

logger = structlog.get_logger()


class YouTubeService:
    """Service for searching educational videos on YouTube."""
    
    def __init__(self):
        self.api_url = settings.youtube_api_url
        self.api_key = settings.youtube_api_key
        self.timeout = settings.request_timeout_seconds
        self.educational_channels = settings.youtube_educational_channels
        
    async def _make_request(self, endpoint: str, params: Dict) -> Optional[Dict[Any, Any]]:
        """Make HTTP request to YouTube Data API."""
        if not self.api_key:
            logger.warning("YouTube API key not configured")
            return None
            
        params["key"] = self.api_key
        url = f"{self.api_url}/{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                return response.json()
        except httpx.HTTPError as e:
            logger.warning("YouTube API request failed", url=url, error=str(e))
            return None
        except Exception as e:
            logger.error("Unexpected error in YouTube request", url=url, error=str(e))
            return None
    
    async def search_videos(self, query: str, max_results: int = 10) -> List[YouTubeVideo]:
        """
        Search for videos on YouTube.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of YouTubeVideo objects
        """
        params = {
            "part": "snippet",
            "q": query,
            "type": "video",
            "maxResults": min(max_results, 25),  # YouTube API limit
            "order": "relevance",
            "videoDuration": "medium",  # 4-20 minutes
            "videoDefinition": "any",
            "videoEmbeddable": "true",
            "safeSearch": "strict"
        }
        
        logger.info("Searching YouTube videos", query=query, max_results=max_results)
        
        data = await self._make_request("search", params)
        if not data or "items" not in data:
            return []
        
        videos = []
        video_ids = []
        
        for item in data["items"]:
            video_id = item["id"]["videoId"]
            video_ids.append(video_id)
            
            snippet = item["snippet"]
            
            videos.append(YouTubeVideo(
                video_id=video_id,
                title=snippet["title"],
                channel_title=snippet["channelTitle"],
                duration="",  # Will be filled by get_video_details
                thumbnail_url=snippet["thumbnails"]["high"]["url"],
                description=snippet.get("description", ""),
                published_at=snippet["publishedAt"]
            ))
        
        # Get additional details (duration, etc.)
        if video_ids:
            detailed_videos = await self.get_video_details(video_ids)
            # Merge duration info
            for i, video in enumerate(videos):
                if i < len(detailed_videos) and detailed_videos[i]:
                    video.duration = detailed_videos[i].duration
        
        logger.info("Found YouTube videos", query=query, count=len(videos))
        return videos
    
    async def get_video_details(self, video_ids: List[str]) -> List[Optional[YouTubeVideo]]:
        """
        Get detailed information for videos.
        
        Args:
            video_ids: List of YouTube video IDs
            
        Returns:
            List of YouTubeVideo objects with detailed info
        """
        if not video_ids:
            return []
        
        params = {
            "part": "contentDetails,snippet",
            "id": ",".join(video_ids)
        }
        
        data = await self._make_request("videos", params)
        if not data or "items" not in data:
            return [None] * len(video_ids)
        
        videos = []
        for item in data["items"]:
            snippet = item["snippet"]
            content_details = item["contentDetails"]
            
            videos.append(YouTubeVideo(
                video_id=item["id"],
                title=snippet["title"],
                channel_title=snippet["channelTitle"],
                duration=content_details["duration"],
                thumbnail_url=snippet["thumbnails"]["high"]["url"],
                description=snippet.get("description", ""),
                published_at=snippet["publishedAt"]
            ))
        
        return videos
    
    def _is_educational_channel(self, channel_title: str) -> bool:
        """Check if channel is in educational whitelist."""
        channel_lower = channel_title.lower()
        return any(edu_channel.lower() in channel_lower for edu_channel in self.educational_channels)
    
    def _calculate_video_score(self, video: YouTubeVideo, query: str) -> float:
        """Calculate relevance score for video."""
        score = 0.0
        
        # Educational channel bonus
        if self._is_educational_channel(video.channel_title):
            score += 2.0
        
        # Title relevance
        title_lower = video.title.lower()
        query_lower = query.lower()
        
        if query_lower in title_lower:
            score += 1.5
        
        # Educational keywords in title
        educational_keywords = [
            "chemistry", "experiment", "reaction", "synthesis", 
            "laboratory", "demo", "tutorial", "lesson", "education"
        ]
        
        for keyword in educational_keywords:
            if keyword in title_lower:
                score += 0.5
        
        # Avoid non-educational content
        avoid_keywords = [
            "music", "song", "funny", "prank", "meme", "gaming"
        ]
        
        for keyword in avoid_keywords:
            if keyword in title_lower:
                score -= 1.0
        
        return max(0.0, score)
    
    async def search_educational_videos(self, query: str, limit: int = 5) -> List[MediaVideo]:
        """
        Search for educational videos about a chemical compound.

        Args:
            query: Chemical name or formula
            limit: Maximum number of videos to return

        Returns:
            List of MediaVideo objects
        """
        if not self.api_key:
            logger.warning("YouTube API key not configured, skipping video search")
            return []

        # Check cache first
        cached_videos = await cache_manager.get_videos_cache(query)
        if cached_videos:
            logger.info("Found YouTube videos in cache", query=query, count=len(cached_videos))
            return [MediaVideo(**video) for video in cached_videos[:limit]]

        # Expand query for YouTube search
        expanded_queries = QueryNormalizer.expand_query_for_search(query, "youtube")
        
        all_videos = []
        
        # Search with different query variations
        for search_query in expanded_queries[:3]:  # Limit API calls
            videos = await self.search_videos(search_query, max_results=10)
            all_videos.extend(videos)
        
        # Score and sort videos
        scored_videos = []
        for video in all_videos:
            score = self._calculate_video_score(video, query)
            scored_videos.append((score, video))
        
        # Sort by score (descending) and remove duplicates
        scored_videos.sort(key=lambda x: x[0], reverse=True)
        
        seen_video_ids = set()
        unique_videos = []
        
        for score, video in scored_videos:
            if video.video_id not in seen_video_ids and len(unique_videos) < limit:
                seen_video_ids.add(video.video_id)
                unique_videos.append(video)
        
        # Convert to MediaVideo format
        media_videos = []
        for video in unique_videos:
            media_videos.append(MediaVideo(
                video_id=video.video_id,
                url=f"https://www.youtube.com/watch?v={video.video_id}",
                title=video.title,
                channel=video.channel_title,
                duration=video.duration,
                thumbnails=[video.thumbnail_url],
                source="youtube",
                license="YouTube Standard License"
            ))
        
        # Cache the results
        if media_videos:
            await cache_manager.set_videos_cache(
                query, [video.model_dump() for video in media_videos]
            )

        logger.info("Found educational YouTube videos", query=query, count=len(media_videos))
        return media_videos
