"""Redis cache utilities for Chemistry Media API."""

import json
import hashlib
from typing import Optional, Any, Dict
import aioredis
import structlog
from contextlib import asynccontextmanager

from app.config import settings

logger = structlog.get_logger()


class CacheManager:
    """Redis cache manager for API responses."""
    
    def __init__(self):
        self.redis_url = settings.redis_url
        self.ttl = settings.cache_ttl_seconds
        self._redis = None
    
    async def connect(self):
        """Connect to Redis."""
        try:
            self._redis = aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            # Test connection
            await self._redis.ping()
            logger.info("Connected to <PERSON><PERSON>", url=self.redis_url)
        except Exception as e:
            logger.warning("Failed to connect to Red<PERSON>", error=str(e))
            self._redis = None
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self._redis:
            await self._redis.close()
            logger.info("Disconnected from Redis")
    
    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """Generate cache key from parameters."""
        # Create a deterministic key from parameters
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self._redis:
            return None
        
        try:
            value = await self._redis.get(key)
            if value:
                return json.loads(value)
        except Exception as e:
            logger.warning("Cache get failed", key=key, error=str(e))
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        if not self._redis:
            return False
        
        try:
            ttl = ttl or self.ttl
            serialized_value = json.dumps(value, default=str)
            await self._redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.warning("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        if not self._redis:
            return False
        
        try:
            await self._redis.delete(key)
            return True
        except Exception as e:
            logger.warning("Cache delete failed", key=key, error=str(e))
            return False
    
    async def get_compound_cache(self, query: str) -> Optional[Dict]:
        """Get cached compound data."""
        key = self._generate_cache_key("compound", query=query.lower())
        return await self.get(key)
    
    async def set_compound_cache(self, query: str, compound_data: Dict) -> bool:
        """Cache compound data."""
        key = self._generate_cache_key("compound", query=query.lower())
        return await self.set(key, compound_data)
    
    async def get_images_cache(self, query: str, source: str) -> Optional[list]:
        """Get cached images data."""
        key = self._generate_cache_key("images", query=query.lower(), source=source)
        return await self.get(key)
    
    async def set_images_cache(self, query: str, source: str, images_data: list) -> bool:
        """Cache images data."""
        key = self._generate_cache_key("images", query=query.lower(), source=source)
        return await self.set(key, images_data)
    
    async def get_videos_cache(self, query: str) -> Optional[list]:
        """Get cached videos data."""
        key = self._generate_cache_key("videos", query=query.lower())
        return await self.get(key)
    
    async def set_videos_cache(self, query: str, videos_data: list) -> bool:
        """Cache videos data."""
        key = self._generate_cache_key("videos", query=query.lower())
        return await self.set(key, videos_data)
    
    async def get_search_cache(self, query: str, types: list, limit: int, lang: str) -> Optional[Dict]:
        """Get cached search results."""
        key = self._generate_cache_key(
            "search", 
            query=query.lower(), 
            types=sorted(types), 
            limit=limit, 
            lang=lang
        )
        return await self.get(key)
    
    async def set_search_cache(self, query: str, types: list, limit: int, lang: str, 
                              search_data: Dict) -> bool:
        """Cache search results."""
        key = self._generate_cache_key(
            "search", 
            query=query.lower(), 
            types=sorted(types), 
            limit=limit, 
            lang=lang
        )
        # Cache for shorter time for full search results
        return await self.set(key, search_data, ttl=3600)  # 1 hour


# Global cache manager instance
cache_manager = CacheManager()


@asynccontextmanager
async def get_cache():
    """Context manager for cache operations."""
    if not cache_manager._redis:
        await cache_manager.connect()
    
    try:
        yield cache_manager
    finally:
        pass  # Keep connection alive for reuse
