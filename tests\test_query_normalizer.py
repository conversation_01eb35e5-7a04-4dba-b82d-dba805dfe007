"""Tests for query normalization utilities."""

import pytest
from app.utils.query_normalizer import QueryNormalizer


class TestQueryNormalizer:
    """Test query normalization functionality."""
    
    def test_normalize_unicode(self):
        """Test Unicode normalization."""
        text = "Axit sunfuric"
        normalized = QueryNormalizer.normalize_unicode(text)
        assert normalized == "Axit sunfuric"
        
        # Test with extra whitespace
        text_with_spaces = "  Axit sunfuric  "
        normalized = QueryNormalizer.normalize_unicode(text_with_spaces)
        assert normalized == "Axit sunfuric"
    
    def test_remove_diacritics(self):
        """Test Vietnamese diacritics removal."""
        test_cases = [
            ("axit", "axit"),  # No diacritics
            ("axit sunfuric", "axit sunfuric"),  # No diacritics
            ("đồng", "dong"),  # Vietnamese đ
            ("sắt", "sat"),  # Vietnamese ắ
            ("l<PERSON>u huỳnh", "luu huynh"),  # Multiple diacritics
            ("<PERSON><PERSON><PERSON>", "<PERSON>"),  # Uppercase
        ]
        
        for input_text, expected in test_cases:
            result = QueryNormalizer.remove_diacritics(input_text)
            assert result == expected, f"Failed for '{input_text}': got '{result}', expected '{expected}'"
    
    def test_is_chemical_formula(self):
        """Test chemical formula detection."""
        # Valid formulas
        valid_formulas = [
            "H2SO4",
            "NaCl", 
            "C2H5OH",
            "CaCO3",
            "H2O",
            "CO2",
            "NH3",
            "CH4"
        ]
        
        for formula in valid_formulas:
            assert QueryNormalizer.is_chemical_formula(formula), f"'{formula}' should be detected as formula"
        
        # Invalid formulas (names)
        invalid_formulas = [
            "sulfuric acid",
            "axit sunfuric",
            "water",
            "sodium chloride",
            "H2SO4 acid",  # Contains non-formula text
            "123",  # Just numbers
            ""  # Empty
        ]
        
        for name in invalid_formulas:
            assert not QueryNormalizer.is_chemical_formula(name), f"'{name}' should not be detected as formula"
    
    def test_translate_vietnamese_to_english(self):
        """Test Vietnamese to English translation."""
        test_cases = [
            # Acids
            ("axit sunfuric", "sulfuric acid"),
            ("axit sulfuric", "sulfuric acid"),
            ("axit clohidric", "hydrochloric acid"),
            ("axit nitric", "nitric acid"),
            
            # Elements
            ("hiđrô", "hydrogen"),
            ("hidro", "hydrogen"),
            ("oxy", "oxygen"),
            ("oxi", "oxygen"),
            ("sắt", "iron"),
            ("đồng", "copper"),
            ("vàng", "gold"),
            
            # Compounds
            ("nước", "water"),
            ("muối ăn", "sodium chloride"),
            ("rượu etylic", "ethanol"),
            ("cồn", "ethanol"),
            
            # Non-existent mapping
            ("unknown compound", None),
        ]
        
        for vietnamese, expected_english in test_cases:
            result = QueryNormalizer.translate_vietnamese_to_english(vietnamese)
            assert result == expected_english, f"Translation failed for '{vietnamese}'"
    
    def test_translate_without_diacritics(self):
        """Test translation works without diacritics."""
        # Test that translation works even when diacritics are removed
        test_cases = [
            ("sat", "iron"),  # sắt without diacritics
            ("dong", "copper"),  # đồng without diacritics
            ("luu huynh", "sulfur"),  # lưu huỳnh without diacritics
        ]
        
        for input_text, expected in test_cases:
            result = QueryNormalizer.translate_vietnamese_to_english(input_text)
            assert result == expected, f"Translation without diacritics failed for '{input_text}'"
    
    def test_normalize_query(self):
        """Test complete query normalization."""
        test_cases = [
            # Chemical formulas should remain unchanged
            ("H2SO4", ("H2SO4", [])),
            ("NaCl", ("NaCl", [])),
            
            # Vietnamese names should be translated
            ("axit sunfuric", ("sulfuric acid", ["axit sunfuric"])),
            ("đồng", ("copper", ["đồng"])),
            
            # Names with diacritics
            ("sắt", ("iron", ["sắt"])),
            
            # Unknown compounds
            ("unknown compound", ("unknown compound", [])),
        ]
        
        for input_query, (expected_primary, expected_alternatives) in test_cases:
            primary, alternatives = QueryNormalizer.normalize_query(input_query)
            assert primary == expected_primary, f"Primary query failed for '{input_query}'"
            # Check that expected alternatives are present (order may vary)
            for alt in expected_alternatives:
                assert alt in alternatives or alt == primary, f"Alternative '{alt}' missing for '{input_query}'"
    
    def test_expand_query_for_search(self):
        """Test query expansion for different search contexts."""
        query = "sulfuric acid"
        
        # YouTube expansion
        youtube_queries = QueryNormalizer.expand_query_for_search(query, "youtube")
        assert len(youtube_queries) > 0
        assert any("chemistry" in q for q in youtube_queries)
        assert any("experiment" in q for q in youtube_queries)
        
        # Wikipedia expansion
        wikipedia_queries = QueryNormalizer.expand_query_for_search(query, "wikipedia")
        assert "sulfuric acid" in wikipedia_queries
        
        # General expansion
        general_queries = QueryNormalizer.expand_query_for_search(query, "general")
        assert "sulfuric acid" in general_queries
    
    def test_expand_query_vietnamese(self):
        """Test query expansion with Vietnamese input."""
        query = "axit sunfuric"
        
        # Should translate and expand
        youtube_queries = QueryNormalizer.expand_query_for_search(query, "youtube")
        assert len(youtube_queries) > 0
        # Should contain both Vietnamese and English variations
        query_text = " ".join(youtube_queries).lower()
        assert "sulfuric acid" in query_text or "axit sunfuric" in query_text
