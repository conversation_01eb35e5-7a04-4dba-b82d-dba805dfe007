"""Tests for Chemistry Media API endpoints."""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from app.main import app
from app.models.schemas import PubChemCompound, MediaImage, MediaVideo


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
def mock_chemspider_compound():
    """Mock ChemSpider compound data."""
    return PubChemCompound(
        cid=1118,
        title="Sulfuric acid",
        iupac_name="Sulfuric acid",
        smiles="O=S(=O)(O)O",
        inchi_key="QAOWNCQODCNURD-UHFFFAOYSA-N",
        molecular_formula="H2O4S",
        molecular_weight=98.079
    )


@pytest.fixture
def mock_images():
    """Mock image data."""
    return [
        MediaImage(
            url="https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/1118/PNG",
            thumbnail="https://pubchem.ncbi.nlm.nih.gov/rest/pug/compound/cid/1118/PNG?image_size=small",
            format="png",
            source="chemspider",
            license="Royal Society of Chemistry",
            attribution="ChemSpider, Royal Society of Chemistry",
            caption="Cấu trúc 2D"
        )
    ]


@pytest.fixture
def mock_videos():
    """Mock video data."""
    return [
        MediaVideo(
            video_id="test123",
            url="https://www.youtube.com/watch?v=test123",
            title="Sulfuric Acid Chemistry",
            channel="Chemistry Channel",
            duration="PT5M30S",
            thumbnails=["https://i.ytimg.com/vi/test123/hqdefault.jpg"],
            source="youtube",
            license="YouTube Standard License"
        )
    ]


class TestHealthEndpoint:
    """Test health check endpoints."""
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Chemistry Media API"
        assert "version" in data
        assert "docs" in data
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_chem_health_endpoint(self, client):
        """Test chemistry service health endpoint."""
        response = client.get("/chem/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"


class TestMediaSearchEndpoint:
    """Test media search endpoint."""

    @patch('app.utils.auth_middleware.verify_api_token')
    def test_search_validation_empty_query(self, mock_auth, client):
        """Test validation with empty query."""
        mock_auth.return_value = "test_client_id"

        response = client.post("/chemistry/media/search", json={
            "query": "",
            "types": ["image"],
            "limit": 5
        })
        assert response.status_code == 422  # Validation error
    
    @patch('app.utils.auth_middleware.verify_api_token')
    def test_search_validation_invalid_types(self, mock_auth, client):
        """Test validation with invalid types."""
        mock_auth.return_value = "test_client_id"

        response = client.post("/chemistry/media/search", json={
            "query": "H2SO4",
            "types": ["invalid"],
            "limit": 5
        })
        assert response.status_code == 422  # Validation error
    
    @patch('app.utils.auth_middleware.verify_api_token')
    def test_search_validation_invalid_limit(self, mock_auth, client):
        """Test validation with invalid limit."""
        mock_auth.return_value = "test_client_id"

        response = client.get("/chemistry/media/search", json={
            "query": "H2SO4",
            "types": ["image"],
            "limit": 100  # Over max limit
        })
        assert response.status_code == 422  # Validation error

    @patch('app.utils.auth_middleware.verify_api_token')
    def test_search_validation_gif_type(self, mock_auth, client):
        """Test validation with gif type."""
        mock_auth.return_value = "test_client_id"

        response = client.post("/chemistry/media/search", json={
            "query": "H2O",
            "types": ["gif"],
            "limit": 5
        })
        assert response.status_code == 200  # Should be valid
    
    @patch('app.utils.auth_middleware.verify_api_token')
    @patch('app.services.chemspider_service.ChemSpiderService.search_compound')
    @patch('app.services.chemspider_service.ChemSpiderService.get_compound_images')
    @patch('app.services.wikipedia_service.WikipediaService.search_images')
    @patch('app.services.youtube_service.YouTubeService.search_educational_videos')
    def test_search_success_formula(self, mock_youtube, mock_wikipedia,
                                   mock_chemspider_images, mock_chemspider_search, mock_auth,
                                   client, mock_chemspider_compound, mock_images, mock_videos):
        """Test successful search with chemical formula."""
        # Setup mocks
        mock_auth.return_value = "test_client_id"
        mock_chemspider_search.return_value = mock_chemspider_compound
        mock_chemspider_images.return_value = mock_images
        mock_wikipedia.return_value = []
        mock_youtube.return_value = mock_videos
        
        response = client.get("/chemistry/media/search", json={
            "query": "H2SO4",
            "types": ["image", "video"],
            "limit": 10,
            "lang": "vi"
        })
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "query" in data
        assert "resolved" in data
        assert "images" in data
        assert "videos" in data
        assert "meta" in data
        
        # Check resolved compound
        assert data["resolved"]["cid"] == 1118
        assert data["resolved"]["title"] == "Sulfuric acid"
        
        # Check images
        assert len(data["images"]) > 0
        assert data["images"][0]["source"] == "chemspider"
        
        # Check videos
        assert len(data["videos"]) > 0
        assert data["videos"][0]["source"] == "youtube"
        
        # Check metadata
        assert "took_ms" in data["meta"]
        assert "sources" in data["meta"]
    
    @patch('app.services.chemspider_service.ChemSpiderService.search_compound')
    @patch('app.services.wikipedia_service.WikipediaService.search_images')
    def test_search_images_only(self, mock_wikipedia, mock_chemspider_search,
                               client, mock_chemspider_compound, mock_images):
        """Test search with images only."""
        # Setup mocks
        mock_chemspider_search.return_value = mock_chemspider_compound
        mock_wikipedia.return_value = []

        with patch.object(mock_chemspider_search.return_value, 'cid', 1118):
            with patch('app.services.chemspider_service.ChemSpiderService.get_compound_images',
                      return_value=mock_images):
                response = client.get("/chemistry/media/search", json={
                    "query": "axit sunfuric",
                    "types": ["image"],
                    "limit": 5,
                    "lang": "vi"
                })
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["images"]) > 0
        assert len(data["videos"]) == 0
    
    @patch('app.services.chemspider_service.ChemSpiderService.search_compound')
    @patch('app.services.youtube_service.YouTubeService.search_educational_videos')
    def test_search_videos_only(self, mock_youtube, mock_chemspider_search,
                               client, mock_videos):
        """Test search with videos only."""
        # Setup mocks
        mock_chemspider_search.return_value = None  # No compound found
        mock_youtube.return_value = mock_videos
        
        response = client.get("/chemistry/media/search", json={
            "query": "chemistry experiment",
            "types": ["video"],
            "limit": 5,
            "lang": "en"
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["images"]) == 0
        assert len(data["videos"]) > 0
        assert data["resolved"] is None
    
    @patch('app.services.chemspider_service.ChemSpiderService.search_compound')
    def test_search_no_results(self, mock_chemspider_search, client):
        """Test search with no results."""
        # Setup mocks to return empty results
        mock_chemspider_search.return_value = None
        
        with patch('app.services.wikipedia_service.WikipediaService.search_images', 
                  return_value=[]):
            with patch('app.services.youtube_service.YouTubeService.search_educational_videos',
                      return_value=[]):
                response = client.get("/chemistry/media/search", params={
                    "query": "nonexistent_compound_xyz",
                    "types": ["image", "video"],
                    "limit": 5
                })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["resolved"] is None
        assert len(data["images"]) == 0
        assert len(data["videos"]) == 0
        assert data["meta"]["took_ms"] > 0
