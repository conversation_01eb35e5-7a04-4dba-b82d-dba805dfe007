"""Pydantic models for Chemistry Media API."""

from typing import List, Optional, Literal, Generic, TypeVar
from pydantic import BaseModel, Field, validator
from datetime import datetime

T = TypeVar('T')
import re


class MediaSearchRequest(BaseModel):
    """Request model for media search."""
    
    query: str = Field(..., description="Tên chất hoặc công thức hóa học", max_length=200)
    types: List[Literal["image", "video", "gif"]] = Field(
        default=["image", "video"],
        description="Loại media cần tìm (image, video, gif)"
    )
    limit: int = Field(default=12, ge=1, le=50, description="Số lượng kết quả tối đa")
    lang: Literal["vi", "en"] = Field(default="vi", description="Ngôn ngữ")
    
    @validator("query")
    def validate_query(cls, v):
        """Validate and clean query."""
        v = v.strip()
        if not v:
            raise ValueError("Query không được để trống")
        return v
    
    @validator("types")
    def validate_types(cls, v):
        """Validate types list."""
        if not v:
            raise ValueError("Phải chọn ít nhất một loại media")
        return list(set(v))  # Remove duplicates


class ChemicalCompound(BaseModel):
    """Resolved chemical compound information."""

    cid: Optional[int] = Field(None, description="PubChem Compound ID")
    title: Optional[str] = Field(None, description="Tên chính thức")
    iupac_name: Optional[str] = Field(None, description="Tên IUPAC")
    smiles: Optional[str] = Field(None, description="SMILES notation")
    inchi_key: Optional[str] = Field(None, description="InChI Key")


class PubChemCompound(BaseModel):
    """Chemical compound information from PubChem."""

    cid: int = Field(..., description="PubChem Compound ID")
    title: Optional[str] = Field(None, description="Tên chất")
    iupac_name: Optional[str] = Field(None, description="Tên IUPAC")
    smiles: Optional[str] = Field(None, description="SMILES notation")
    inchi_key: Optional[str] = Field(None, description="InChI Key")
    molecular_formula: Optional[str] = Field(None, description="Công thức phân tử")
    molecular_weight: Optional[float] = Field(None, description="Khối lượng phân tử")


class MediaImage(BaseModel):
    """Image media item."""
    
    url: str = Field(..., description="URL của hình ảnh")
    thumbnail: Optional[str] = Field(None, description="URL thumbnail")
    format: str = Field(..., description="Định dạng file (png, jpg, svg)")
    width: Optional[int] = Field(None, description="Chiều rộng (pixels)")
    height: Optional[int] = Field(None, description="Chiều cao (pixels)")
    source: str = Field(..., description="Nguồn (pubchem, wikipedia, rdkit)")
    license: str = Field(..., description="Giấy phép sử dụng")
    attribution: str = Field(..., description="Ghi công nguồn")
    caption: Optional[str] = Field(None, description="Mô tả hình ảnh")


class MediaVideo(BaseModel):
    """Video media item."""
    
    video_id: str = Field(..., description="ID video")
    url: str = Field(..., description="URL video")
    title: str = Field(..., description="Tiêu đề video")
    channel: str = Field(..., description="Kênh YouTube")
    duration: str = Field(..., description="Thời lượng (ISO 8601)")
    thumbnails: List[str] = Field(default_factory=list, description="URLs thumbnail")
    source: str = Field(..., description="Nguồn (youtube)")
    license: str = Field(..., description="Giấy phép sử dụng")


class SearchMetadata(BaseModel):
    """Search metadata."""
    
    took_ms: int = Field(..., description="Thời gian xử lý (milliseconds)")
    sources: List[str] = Field(..., description="Các nguồn đã sử dụng")


class MediaSearchResponse(BaseModel):
    """Response model for media search."""

    query: str = Field(..., description="Query gốc")
    resolved: Optional[ChemicalCompound] = Field(None, description="Thông tin chất hóa học")
    images: List[MediaImage] = Field(default_factory=list, description="Danh sách hình ảnh")
    gifs: List[MediaImage] = Field(default_factory=list, description="Danh sách GIF animations")
    videos: List[MediaVideo] = Field(default_factory=list, description="Danh sách video")
    meta: SearchMetadata = Field(..., description="Metadata tìm kiếm")


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(..., description="Mã lỗi")
    message: str = Field(..., description="Thông báo lỗi")
    details: Optional[dict] = Field(None, description="Chi tiết lỗi")


# Internal models for services

class PubChemCompound(BaseModel):
    """PubChem compound data."""
    
    cid: int
    title: Optional[str] = None
    iupac_name: Optional[str] = None
    smiles: Optional[str] = None
    inchi_key: Optional[str] = None
    molecular_formula: Optional[str] = None
    molecular_weight: Optional[float] = None


class WikipediaImage(BaseModel):
    """Wikipedia/Commons image data."""
    
    url: str
    title: str
    description: Optional[str] = None
    license: str
    attribution: str
    width: Optional[int] = None
    height: Optional[int] = None


class YouTubeVideo(BaseModel):
    """YouTube video data."""

    video_id: str
    title: str
    channel_title: str
    duration: str
    thumbnail_url: str
    description: Optional[str] = None
    published_at: Optional[str] = None


# Response wrapper models
class ApiResponse(BaseModel):
    """Standard API response wrapper."""

    success: bool = Field(..., description="Indicates if the request was successful")
    message: str = Field(default="", description="Response message or error description")
    data: Optional[dict] = Field(None, description="Response data")


def create_success_response(data: any, message: str = "") -> dict:
    """Create a successful API response."""
    return {
        "success": True,
        "message": message,
        "data": data
    }


def create_error_response(message: str) -> dict:
    """Create an error API response."""
    return {
        "success": False,
        "message": message,
        "data": None
    }
