"""
Simple auth test
"""

import asyncio
import httpx
import json


async def test_auth_simple():
    """Test auth endpoints only"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        print("=== Simple Auth Test ===\n")
        
        # 1. Register client
        print("1. Registering client...")
        register_data = {
            "client_name": "Simple Test Client",
            "description": "Simple test",
            "contact_email": "<EMAIL>"
        }
        
        response = await client.post(
            f"{base_url}/chem/auth/register-client",
            json=register_data
        )
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            registration = response.json()
            client_id = registration['client_id']
            client_secret = registration['client_secret']
            print(f"✓ Client registered: {client_id}\n")
            
            # 2. Generate token
            print("2. Generating token...")
            token_data = {
                "client_id": client_id,
                "client_secret": client_secret
            }
            
            response = await client.post(
                f"{base_url}/chem/auth/token",
                json=token_data
            )
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                token_response = response.json()
                access_token = token_response['access_token']
                print(f"✓ Token generated successfully\n")
                
                # 3. Verify token
                print("3. Verifying token...")
                verify_data = {"token": access_token}
                response = await client.post(
                    f"{base_url}/chem/auth/verify-token",
                    json=verify_data
                )
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    print("✓ Token verified successfully\n")
                    
                    # 4. Get client info
                    print("4. Getting client info...")
                    headers = {"Authorization": f"Bearer {access_token}"}
                    response = await client.get(
                        f"{base_url}/chem/auth/client-info",
                        headers=headers
                    )
                    print(f"Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        print("✓ Client info retrieved successfully\n")
                        print("🎉 Auth system integration successful!")
                        return True
                    else:
                        print(f"✗ Failed to get client info: {response.text}")
                else:
                    print(f"✗ Token verification failed: {response.text}")
            else:
                print(f"✗ Token generation failed: {response.text}")
        else:
            print(f"✗ Client registration failed: {response.text}")
    
    return False


if __name__ == "__main__":
    success = asyncio.run(test_auth_simple())
    if success:
        print("\n✅ Auth integration test passed!")
    else:
        print("\n❌ Auth integration test failed!")
