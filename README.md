# Chemistry Media API

API để tìm kiếm hình ảnh và video liên quan đến chất hóa học dựa trên tên hoặc công thức hóa học.

## Tính năng

- **Tìm kiếm thông minh**: Tự động chuyển đổi công thức hóa học (H2 → hydrogen, CO2 → carbon dioxide)
- **Tìm kiếm trực tiếp**: Hỗ trợ tiếng Việt và tiếng <PERSON>h, tìm trực tiếp không cần dịch
- **Đa loại media**: Hình ảnh, GIF animations, và video
- **Đa nguồn dữ liệu**: ChemSpider, PubChem, Wikipedia/Wikimedia Commons, YouTube
- **Cache thông minh**: Redis caching để tiết kiệm API tokens
- **Xác thực bảo mật**: JWT-based authentication
- **Metadata đầy đủ**: Thông tin bản quyền, ng<PERSON>ồn, kích thước

## Cài đặt

1. Clone repository:

```bash
git clone <repository-url>
cd chemistry
```

2. Tạo virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate  # Windows
```

3. Cài đặt dependencies:

```bash
pip install -r requirements.txt
```

4. Cấu hình environment variables:

```bash
cp .env.example .env
# Chỉnh sửa .env với API keys của bạn
```

5. Chạy ứng dụng:

```bash
python run.py
# hoặc
python -m app.main
# hoặc với uvicorn trực tiếp
uvicorn app.main:app --reload
```

## API Documentation

Sau khi chạy ứng dụng, truy cập:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Ví dụ sử dụng

```bash
curl -X POST "http://localhost:8000/chemistry/media/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "query": "H2SO4",
    "types": ["image", "video", "gif"],
    "limit": 8,
    "lang": "vi"
  }'
```

### Response Format

Tất cả API responses đều có format chuẩn:

```json
{
  "success": true,
  "message": "Tìm kiếm thành công",
  "data": {
    "images": [...],
    "gifs": [...],
    "videos": [...],
    "metadata": {
      "query": "h2so4",
      "resolved_query": "sulfuric acid",
      "total_results": 25,
      "search_time_ms": 1234,
      "sources": ["pubchem", "chemspider", "wikipedia", "youtube"]
    }
  }
}
```

**Error responses:**

```json
{
  "success": false,
  "message": "Không thể thực hiện tìm kiếm: API key invalid",
  "data": null
}
```

## Cấu trúc dự án

```
chemistry/
├── app/
│   ├── api/          # API routes
│   ├── models/       # Pydantic models
│   ├── services/     # Business logic
│   ├── utils/        # Utilities
│   ├── config.py     # Configuration
│   └── main.py       # FastAPI app
├── tests/            # Test files
├── requirements.txt  # Dependencies
└── README.md
```

## Chạy Tests

```bash
# Chạy tất cả tests
pytest

# Chạy tests với coverage
pytest --cov=app

# Chạy tests cụ thể
pytest tests/test_api.py
pytest tests/test_query_normalizer.py
```

## Cấu hình API Keys

### ChemSpider API

1. Truy cập [ChemSpider Developer Portal](https://developer.rsc.org/)
2. Đăng ký tài khoản miễn phí
3. Tạo API key
4. Thêm vào file `.env`:

```
CHEMSPIDER_API_KEY=your_chemspider_api_key_here
```

### YouTube Data API

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Enable YouTube Data API v3
4. Tạo API key
5. Thêm vào file `.env`:

```
YOUTUBE_API_KEY=your_api_key_here
```

### Redis (Khuyến nghị)

Redis được sử dụng để cache kết quả tìm kiếm, giúp:

- **Tiết kiệm token API**: Tránh gọi lại API cho cùng một chất hóa học
- **Tăng tốc độ**: Trả về kết quả ngay lập tức cho queries đã cache
- **Giảm tải server**: Ít request đến external APIs

```bash
# Cài đặt Redis (Ubuntu/Debian)
sudo apt-get install redis-server

# Hoặc với Docker
docker run -d -p 6379:6379 redis:alpine
```

**Cache Strategy:**

- Compound data: 24 giờ
- Images/Videos: 24 giờ
- Full search results: 1 giờ

## Smart Query Processing

### Formula Resolution

API tự động nhận diện và chuyển đổi công thức hóa học thành tên phổ biến:

```json
{
  "query": "h2",
  "types": ["image", "video"]
}
```

**Dual API approach**: PubChem + ChemSpider

- **Primary**: "hydrogen" (từ PubChem/ChemSpider)
- **Alternatives**: "h2", "dihydrogen", "hydrogen gas"

### Direct Search

API tìm kiếm trực tiếp với query gốc, không cần dịch:

```json
{
  "query": "axit sunfuric",
  "types": ["image"]
}
```

**Search strategy**:

- **Chemical formulas**: Resolve qua PubChem/ChemSpider APIs
- **Vietnamese terms**: Search trực tiếp trên các platforms
- **English terms**: Search trực tiếp

**Ví dụ**:

- `h2so4` → Resolve thành "sulfuric acid" → Search
- `nước` → Search trực tiếp "nước" trên Wikipedia/YouTube
- `water` → Search trực tiếp "water"

## Ví dụ Response

```json
{
  "query": "H2SO4",
  "resolved": {
    "cid": 1086,
    "title": "Sulfuric acid",
    "iupac_name": "Sulfuric acid",
    "smiles": "O=S(=O)(O)O",
    "inchi_key": "QAOWNCQODCNURD-UHFFFAOYNA-N"
  },
  "images": [
    {
      "url": "https://api.rsc.org/compounds/v1/records/1086/image",
      "format": "png",
      "source": "chemspider",
      "license": "Royal Society of Chemistry",
      "attribution": "ChemSpider, Royal Society of Chemistry",
      "caption": "Cấu trúc 2D"
    }
  ],
  "gifs": [
    {
      "url": "https://commons.wikimedia.org/wiki/File:Example.gif",
      "format": "gif",
      "source": "wikipedia",
      "license": "CC BY-SA 4.0",
      "attribution": "Wikimedia Commons",
      "caption": "GIF animation về H2SO4"
    }
  ],
  "videos": [
    {
      "video_id": "abc123",
      "url": "https://www.youtube.com/watch?v=abc123",
      "title": "Sulfuric Acid Chemistry",
      "channel": "Chemistry Channel",
      "duration": "PT5M30S",
      "source": "youtube",
      "license": "YouTube Standard License"
    }
  ],
  "meta": {
    "took_ms": 8003,
    "sources": ["chemspider", "wikipedia", "youtube"]
  }
}
```

## License

MIT License
