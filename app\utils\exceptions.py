"""Custom exceptions for Chemistry Media API."""

from typing import Optional, Dict, Any


class ChemistryAPIException(Exception):
    """Base exception for Chemistry Media API."""
    
    def __init__(self, message: str, error_code: str = "UNKNOWN_ERROR", 
                 details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class CompoundNotFoundError(ChemistryAPIException):
    """Raised when chemical compound is not found."""
    
    def __init__(self, query: str):
        super().__init__(
            message=f"Không tìm thấy thông tin cho chất: {query}",
            error_code="COMPOUND_NOT_FOUND",
            details={"query": query}
        )


class ExternalAPIError(ChemistryAPIException):
    """Raised when external API call fails."""
    
    def __init__(self, service: str, message: str, status_code: Optional[int] = None):
        super().__init__(
            message=f"Lỗi khi gọi {service}: {message}",
            error_code="EXTERNAL_API_ERROR",
            details={"service": service, "status_code": status_code}
        )


class RateLimitError(ChemistryAPIException):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, service: str):
        super().__init__(
            message=f"Đã vượt quá giới hạn tần suất cho {service}",
            error_code="RATE_LIMIT_EXCEEDED",
            details={"service": service}
        )


class ValidationError(ChemistryAPIException):
    """Raised when input validation fails."""
    
    def __init__(self, field: str, message: str):
        super().__init__(
            message=f"Lỗi validation cho {field}: {message}",
            error_code="VALIDATION_ERROR",
            details={"field": field}
        )


class CacheError(ChemistryAPIException):
    """Raised when cache operation fails."""
    
    def __init__(self, operation: str, message: str):
        super().__init__(
            message=f"Lỗi cache {operation}: {message}",
            error_code="CACHE_ERROR",
            details={"operation": operation}
        )


class TimeoutError(ChemistryAPIException):
    """Raised when request times out."""
    
    def __init__(self, service: str, timeout_seconds: int):
        super().__init__(
            message=f"Timeout khi gọi {service} sau {timeout_seconds}s",
            error_code="REQUEST_TIMEOUT",
            details={"service": service, "timeout": timeout_seconds}
        )


class ConfigurationError(ChemistryAPIException):
    """Raised when configuration is invalid."""
    
    def __init__(self, setting: str, message: str):
        super().__init__(
            message=f"Lỗi cấu hình {setting}: {message}",
            error_code="CONFIGURATION_ERROR",
            details={"setting": setting}
        )
