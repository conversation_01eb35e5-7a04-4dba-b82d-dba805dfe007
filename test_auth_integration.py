"""
Script test tích hợp auth system
"""

import asyncio
import httpx
import json


async def test_auth_flow():
    """Test complete auth flow"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        print("=== Testing Auth Integration ===\n")
        
        # 1. Test health check
        print("1. Testing auth health check...")
        response = await client.get(f"{base_url}/chem/auth/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}\n")
        
        # 2. Register a new client
        print("2. Registering new client...")
        register_data = {
            "client_name": "Test Client",
            "description": "Client for testing auth integration",
            "contact_email": "<EMAIL>"
        }
        
        response = await client.post(
            f"{base_url}/chem/auth/register-client",
            json=register_data
        )
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            registration = response.json()
            print(f"Client ID: {registration['client_id']}")
            print(f"Client Secret: {registration['client_secret'][:10]}...")
            print(f"Message: {registration['message']}\n")
            
            client_id = registration['client_id']
            client_secret = registration['client_secret']
            
            # 3. Generate access token
            print("3. Generating access token...")
            token_data = {
                "client_id": client_id,
                "client_secret": client_secret
            }
            
            response = await client.post(
                f"{base_url}/chem/auth/token",
                json=token_data
            )
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                token_response = response.json()
                access_token = token_response['access_token']
                print(f"Access Token: {access_token[:20]}...")
                print(f"Expires in: {token_response['expires_in']} seconds\n")
                
                # 4. Verify token
                print("4. Verifying token...")
                verify_data = {"token": access_token}
                response = await client.post(
                    f"{base_url}/chem/auth/verify-token",
                    json=verify_data
                )
                print(f"Status: {response.status_code}")
                print(f"Response: {response.json()}\n")
                
                # 5. Get client info using token
                print("5. Getting client info with token...")
                headers = {"Authorization": f"Bearer {access_token}"}
                response = await client.get(
                    f"{base_url}/chem/auth/client-info",
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                print(f"Response: {response.json()}\n")
                
                # 6. Test protected endpoint (media search)
                print("6. Testing protected media search endpoint...")
                search_data = {
                    "query": "water",
                    "types": ["image"],
                    "limit": 2
                }
                
                response = await client.post(
                    f"{base_url}/chem/media/search",
                    json=search_data,
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    search_result = response.json()
                    print(f"Found {len(search_result.get('images', []))} images")
                    print(f"Query: {search_result.get('query')}")
                    print(f"Sources: {search_result.get('meta', {}).get('sources', [])}\n")
                else:
                    print(f"Error: {response.text}\n")
                
                # 7. Test without token (should fail)
                print("7. Testing media search without token (should fail)...")
                response = await client.post(
                    f"{base_url}/chem/media/search",
                    json=search_data
                )
                print(f"Status: {response.status_code}")
                print(f"Response: {response.json()}\n")
                
                # 8. Revoke token
                print("8. Revoking token...")
                response = await client.post(
                    f"{base_url}/chem/auth/revoke-token",
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                print(f"Response: {response.json()}\n")
                
                # 9. Test with revoked token (should fail)
                print("9. Testing with revoked token (should fail)...")
                response = await client.get(
                    f"{base_url}/chem/auth/client-info",
                    headers=headers
                )
                print(f"Status: {response.status_code}")
                print(f"Response: {response.json()}\n")
                
            else:
                print(f"Failed to generate token: {response.text}\n")
        else:
            print(f"Failed to register client: {response.text}\n")


if __name__ == "__main__":
    asyncio.run(test_auth_flow())
